import { DialogApiInjection, DialogReactive } from "naive-ui/es/dialog/src/DialogProvider";
import { MessageApiInjection } from "naive-ui/es/message/src/MessageProvider";
import { Ref } from "vue";

export { }
declare global {
    interface Window {
        $message: MessageApiInjection;
        $dialog: DialogApiInjection;
        $dialogReactive: Ref<readonly DialogReactive[]>
        DASHBOARD_REQ_DOWNLOAD_RESOURCE: (url: string, channel: string) => Promise<void>;
        DASHBOARD_START_GAME_CHECK: (channel: string) => Promise<Map<string, string | string[]>>;
        DASHBOARD_REQ_START_GAME: (id: string, url: string, channel: string, name: string) => Promise<number>;
        DASHBOARD_REQ_STOP_GAME: (id: string) => Promise<number>;
        DASHBOARD_REQ_DEL_GAME: (id: string) => Promise<number>;
        DASHBOARD_REQ_GET_GAME: (id: string) => string;
        DASHBOARD_REQ_SET_ZOOM: (id: string, zoom: number) => '' | 'ok';
        DASHBOARD_REQ_GET_MACHINE: () => string;
        GAME_EXEC: (gameid: string, jsCode: string) => any;
        Events: { [key: string]: any },
        ON_UNKNOWN_KEY: (key: string) => void,
        GetThemeAuto: () => boolean,
        GetThemeType: () => boolean,
        request: (url: string) => Promise<any>
        Evt: {
            on: (key: string, call: Function) => void
            emit: (key: string, ...args: any[]) => void
            off: (key: string) => void
        },
        postReq: <T>(url: string, args?: any) => Promise<T>
        getMachine: () => Promise<string>
        gen: (cnt: number, key: string) => Promise<void>
        copy: (str: string, el?: any) => void
        wait: (delay: number) => Promise<number>
        dlg: any

        VERSION: number
        VER_STR: string
        DEBUG: boolean
        LOCAL_HTTP: boolean
        LOCAL_LOGIC: boolean

        sampleNotify: (arg: string) => void;
        // 下载完成
        DownloadFinished: () => void
        // 下载暂停（网络问题）
        DownloadPaused: () => void
        // 下载中断
        DownloadInterrupted: (reason: string) => void
        // 下载进度更新
        DownloadUpdated: (progress: number, currentSpeed: number, receivedBytes: number, totalBytes: number) => void

        subscribeTeam: (leaderId: number, selfId: number, gameId: string) => void
        unSubscribeTeam: (selfId: number, gameId: string) => void
        sendTeamMsg: (leaderId: number, sender: number, gameId: string, msg: string) => void


    }
}
