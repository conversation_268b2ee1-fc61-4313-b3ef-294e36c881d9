package env;

import mgr.BrowserManager;

import java.io.File;
import java.nio.file.Path;

public enum Channel {
    NONE(0, "未知", "", ""),
    WECHAT_MINI_PROGRAM(1, "微信小程序", "wechat", "wechat"),
    TIKTOK_MINI_PROGRAM(2, "抖音小程序", "tiktok", "tiktok"),
    ;


    private final int type;
    private final String name;
    private final String userDataPath;
    private final String symbol;


    private Channel(int type, String name, String userDataPath, String symbol) {
        this.type = type;
        this.name = name;
        this.userDataPath = userDataPath;
        this.symbol = symbol;
    }

    public int getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public String getUserDataPath() {
        return this.userDataPath;
    }

    public String getSymbol() {
        return this.symbol;
    }

    public static Channel of(int type) {
        for (Channel channel : Channel.values()) {
            if (channel.type == type) {
                return channel;
            }
        }
        return Channel.NONE;
    }
}


