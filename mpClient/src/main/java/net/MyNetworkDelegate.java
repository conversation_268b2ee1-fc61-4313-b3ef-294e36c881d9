package net;

import com.teamdev.jxbrowser.net.Network;
import com.teamdev.jxbrowser.net.callback.BeforeUrlRequestCallback;
import env.Channel;
import env.Global;

import java.util.ArrayList;
import java.util.List;

public class MyNetworkDelegate {
    static String myHost = "https://husong.vip/yxmxt/";
    Network network;
    Channel channel;

    public String append(String fileName) {
        return MyNetworkDelegate.myHost + fileName;
    }

    public MyNetworkDelegate(Network network, Channel channel) {
        this.network = network;
        this.channel = channel;
    }

    public String localHost() {
        String symbol = this.channel.getSymbol();
        return symbol + "://index.html/";
    }

    public void doDelegate() {
        String localHost = this.localHost();
        network.set(BeforeUrlRequestCallback.class, params -> {
            String url = params.urlRequest().url();
            if (!url.startsWith(localHost)) {
                // 拦截非本地资源req
                if (url.contains("/native/")) {
                    int protocolEnd = url.indexOf("://") + 3;
                    String withoutProtocol = url.substring(protocolEnd);
                    int start = withoutProtocol.indexOf("/");
                    String target = localHost + withoutProtocol.substring(start + 1);
                    return BeforeUrlRequestCallback.Response.redirect(target);
                }
            }
            //return BeforeUrlRequestCallback.Response.redirect(target);
            return BeforeUrlRequestCallback.Response.proceed();
        });
    }


}
