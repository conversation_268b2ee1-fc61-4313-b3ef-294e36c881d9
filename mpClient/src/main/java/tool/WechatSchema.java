package tool;

import com.teamdev.jxbrowser.net.HttpHeader;
import com.teamdev.jxbrowser.net.HttpStatus;
import com.teamdev.jxbrowser.net.UrlRequestJob;
import com.teamdev.jxbrowser.net.callback.InterceptUrlRequestCallback;
import env.Channel;
import env.Global;
import org.apache.commons.io.FileUtils;

import javax.annotation.CheckForNull;
import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Objects;

public class WechatSchema implements InterceptUrlRequestCallback {

    public WechatSchema() {
    }


    @CheckForNull
    @Override
    public Response on(Params params) {
        UrlRequestJob.Options.Builder builder = UrlRequestJob.Options
                .newBuilder(HttpStatus.OK);
        UrlRequestJob job = null;
        try {
            String symbol = Channel.WECHAT_MINI_PROGRAM.getSymbol();
            String url = params.urlRequest().url();
            url = url.replace(symbol + "://index.html/", "");

            Path basePath = Global.getLocalResourceDir().resolve(Channel.WECHAT_MINI_PROGRAM.getUserDataPath());

            if (Objects.equals(url, "") || Objects.equals(url, "/")) {
                basePath = basePath.resolve("index.html");
            } else {
                basePath = basePath.resolve(url);
            }
//            String property = System.getProperty("user.dir");
//            Path path = Paths.get(property);
//            if (url.equals("")) {
//                path = path.resolve("src/test/remote/index.html");
//            } else {
//                if (!url.contains("src/test/remote/")) {
//                    path = path.resolve("src/test/remote/");
//                }
//                path = path.resolve(url);
//            }
            File file = basePath.toFile();
            if (!file.exists()) {
                return null;
            }
            byte[] data = FileUtils.readFileToByteArray(file);
            String mimeType = getMimeType(url);
            builder.addHttpHeader(HttpHeader.of("Content-Type", mimeType));
            job = params.newUrlRequestJob(builder.build());
            job.write(data);
            job.complete();
        } catch (Exception e) {
            System.out.println("WechatSchema error: " + params.urlRequest().url());
            throw new RuntimeException(e);
        }
        return Response.intercept(job);
    }

    private static String getMimeType(String path) {
        if (path.contains(".html")) {
            return "text/html";
        }
        if (path.contains(".css")) {
            return "text/css";
        }
        if (path.contains(".js")) {
            return "text/javascript";
        }
        if (path.contains(".png")) {
            return "image/png";
        }
        if (path.contains(".jpg")) {
            return "image/jpg";
        }
        return "text/html";
    }
}
