package db

import (
	"context"
	"log"
	"os"
	"reflect"
	"server/ut"
	"sync"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var (
	client   *mongo.Client
	database string
	ok       bool //是否初始化完成

	colMap         map[string]*TableDef[Collection]
	_oneLock       sync.Once
	_operationLock sync.RWMutex
)

// GetCollection 获取db连接对象 千万要记得必须实现MongoTable接口  不然会引起panic
func GetCollection[C Collection](def *TableDef[C]) *mongo.Collection {
	collection := client.Database(database).Collection(def.Name())
	return collection
}

func CreateIndex[C Collection](def *TableDef[C]) {
	collection := GetCollection(def)
	//log.Debug("Start CreateIndex ,table: %s", collection.Name())
	//sTime := time.Now()
	indexModels := make([]mongo.IndexModel, 0)
	// 遍历配置表
	for key, unique := range def.Index() {
		if key != "" {
			indexModels = append(indexModels, mongo.IndexModel{
				Keys:    bson.M{key: 1},
				Options: options.Index().SetUnique(unique),
			})
		}
	}

	indexView := collection.Indexes()
	// 创建多个索引
	if len(indexModels) > 0 {
		if _, err := indexView.CreateMany(context.Background(), indexModels); err != nil {
			log.Fatalf("创建索引失败：%s %s\n", def.Name(), err.Error())
		}
	}
	//log.Debug("End CreateIndex ,table: %s during :%fs", collection.Name(), time.Since(sTime).Seconds())
}

// Init 初始化连接
func Init(url, dbname string) {
	log.Println("Try to connect mongo db, wait for response.")
	database = dbname
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	opt := options.Client().ApplyURI(url)
	opt.SetLocalThreshold(3 * time.Second)  //只使用与mongo操作耗时小于3秒的
	opt.SetMaxConnIdleTime(5 * time.Second) //指定连接可以保持空闲的最大毫秒数
	opt.SetMaxPoolSize(1024)                //使用最大的连接数
	var err error
	if client, err = mongo.Connect(ctx, opt); err == nil {
		err = client.Ping(context.Background(), nil)
		if err != nil {
			log.Fatalf("Connect mongo db err :%s , System exit.", err.Error())
			os.Exit(-1)
		}
		log.Println("mongodb init success! " + url + "[" + database + "]")
		_oneLock.Do(func() {
			colMap = make(map[string]*TableDef[Collection])
		})
	} else if err == mongo.ErrNoDocuments {
		log.Fatalf("mongodb init error! ErrNoDocuments\n")
	} else {
		log.Fatalf(err.Error())
	}

}

// FieldToBsonAuto 获取一个基于obj对象生成的bson.M用来做db保存,需要设置tag:`bson:xxx`
func FieldToBsonAuto(obj interface{}) bson.M {
	v := reflect.ValueOf(obj)
	// 如果值是指针就需要转换
	if v.Kind() == reflect.Pointer {
		// 获取指针指向的对象值
		v = v.Elem()
	}
	t := v.Type()
	r := bson.M{}
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		tag := field.Tag.Get("bson")
		if ut.IsEmpty(tag) {
			tag = field.Tag.Get("json")
		}
		if ut.IsEmpty(tag) || tag == "-" {
			continue // 跳过
		}
		val := reflect.ValueOf(v.Interface()).Field(i)
		if val.Kind() == reflect.Struct || val.Kind() == reflect.Pointer {
			//log.Debug("Save field :%s, instance :% s", field, t)
			if !val.Elem().IsValid() {
				continue
			}
			r[tag] = val.Elem().Interface()
			continue
		}
		r[tag] = val.Interface()
	}
	return r
}

// FieldToBson 获取一个基于传入的fields列表生成的bson.M，用于文档操作,所有fields列表的字段必须都存在于obj上,需要设置tag:`bson:xxx`
func FieldToBson(obj interface{}, fields ...string) bson.M {
	v := reflect.ValueOf(obj)
	// 如果值是指针就需要转换
	if v.Kind() == reflect.Pointer {
		// 获取指针指向的对象值
		v = v.Elem()
	}

	t := v.Type()
	r := bson.M{}
	for _, field := range fields {
		rf, b := t.FieldByName(field)
		if !b {
			//log.Warning("Skip.none field :%s, instance :% s", field, t)
			continue
		}
		bson := rf.Tag.Get("bson")
		if bson == "" {
			//log.Warning("Skip.field has no bson tag:%s, instance :% s", field, t)
			continue
		}
		val := reflect.ValueOf(v.Interface()).FieldByName(field)
		if val.Kind() == reflect.Struct || val.Kind() == reflect.Pointer {
			//log.Warning("Skip.field type error:%s, instance :% s", field, t)
			r[bson] = val.Elem().Interface()
			continue
		}
		r[bson] = val.Interface()
	}
	return r
}
