package db

import (
	"go.mongodb.org/mongo-driver/mongo"
)

type Collection interface {
	Define() (name string, index map[string]bool)
}

// Col
/*
 * @description 获取db连接
 * @param c
 * @return *mongo.Collection
 */
func Col[C Collection](c C) *mongo.Collection {
	name, _ := c.Define()
	def, ok := colMap[name]
	if !ok {
		_operationLock.RLock()
		defer _operationLock.RUnlock()
		def = NewTableDef[Collection](c).CreateIndex()
		colMap[name] = def
	}
	return def.GetCollection()
}

type TableDef[C Collection] struct {
	name  string
	index map[string]bool
}

// CreateIndex 配置索引信息
func (t *TableDef[C]) CreateIndex() *TableDef[C] {
	CreateIndex(t)
	return t
}

func (t *TableDef[C]) GetCollection() *mongo.Collection {
	return GetCollection(t)
}

func (t *TableDef[C]) Name() string {
	return t.name
}

func (t *TableDef[C]) Index() map[string]bool {
	return t.index
}

func NewTableDef[C Collection](c Collection) *TableDef[C] {
	name, index := c.Define()
	return &TableDef[C]{name, index}
}
