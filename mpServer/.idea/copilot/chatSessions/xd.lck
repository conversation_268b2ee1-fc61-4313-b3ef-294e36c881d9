Private property of Exodus: <EMAIL>

jetbrains.exodus.io.LockingManager.lock(LockingManager.kt:88)
jetbrains.exodus.io.LockingManager.lock(LockingManager.kt:39)
jetbrains.exodus.io.FileDataWriter.lock(FileDataWriter.kt:70)
jetbrains.exodus.log.Log.tryLock(Log.kt:804)
jetbrains.exodus.log.Log.<init>(Log.kt:117)
jetbrains.exodus.env.Environments.newLogInstance(Environments.kt:117)
jetbrains.exodus.env.Environments.newLogInstance(Environments.kt:81)
jetbrains.exodus.env.Environments.newLogInstance(Environments.kt:77)
jetbrains.exodus.env.Environments$newInstance$4.invoke(Environments.kt:46)
jetbrains.exodus.env.Environments$newInstance$4.invoke(Environments.kt:46)
jetbrains.exodus.env.Environments.prepare(Environments.kt:120)
jetbrains.exodus.env.Environments.newInstance(Environments.kt:46)
kotlinx.dnq.store.container.EntityStoreHelperKt.createTransientEntityStore(EntityStoreHelper.kt:40)
kotlinx.dnq.store.container.EntityStoreHelperKt.createTransientEntityStore(EntityStoreHelper.kt:31)
kotlinx.dnq.store.container.EntityStoreHelperKt.createTransientEntityStore$default(EntityStoreHelper.kt:30)
com.github.copilot.chat.session.persistence.xodus.XdChatSessionPersistenceService.initStore(XdChatSessionPersistenceService.kt:115)
com.github.copilot.chat.session.persistence.xodus.XdChatSessionPersistenceService.<init>(XdChatSessionPersistenceService.kt:22)
com.github.copilot.chat.session.persistence.xodus.XdChatSessionPersistenceService.<init>(XdChatSessionPersistenceService.kt:15)
com.github.copilot.chat.session.persistence.ChatSessionPersistenceServiceKt.ChatSessionPersistenceService(ChatSessionPersistenceService.kt:43)
com.github.copilot.chat.session.persistence.ChatSessionPersistenceServiceKt.chatSessionsPersistenceService(ChatSessionPersistenceService.kt:53)
com.github.copilot.chat.session.ChatSessionManager.<init>(ChatSessionManager.kt:45)
com.github.copilot.chat.session.ChatSessionManager.<init>(ChatSessionManager.kt:25)
com.github.copilot.chat.window.CopilotChatToolWindow.onCopilotReady(CopilotChatToolWindow.kt:133)
com.github.copilot.chat.window.CopilotChatToolWindow.access$onCopilotReady(CopilotChatToolWindow.kt:40)
com.github.copilot.chat.window.CopilotChatToolWindow$initCopilotStatusListener$1.invoke(CopilotChatToolWindow.kt:118)
com.github.copilot.chat.window.CopilotChatToolWindow$initCopilotStatusListener$1.invoke(CopilotChatToolWindow.kt:115)
com.github.copilot.status.CopilotAuthStatusKt.subscribeToCopilotAuthStatus$lambda$0(CopilotAuthStatus.kt:44)
com.intellij.util.messages.impl.MessageBusImplKt.invokeMethod(MessageBusImpl.kt:680)
com.intellij.util.messages.impl.MessageBusImplKt.invokeListener(MessageBusImpl.kt:644)
com.intellij.util.messages.impl.MessageBusImplKt.deliverMessage(MessageBusImpl.kt:415)
com.intellij.util.messages.impl.MessageBusImplKt.pumpWaiting(MessageBusImpl.kt:394)
com.intellij.util.messages.impl.MessageBusImplKt.access$pumpWaiting(MessageBusImpl.kt:1)
com.intellij.util.messages.impl.MessagePublisher.invoke(MessageBusImpl.kt:454)
jdk.proxy4/jdk.proxy4.$Proxy151.onCopilotStatus(Unknown Source)
com.github.copilot.status.CopilotStatusService.notifyApplication(CopilotStatusService.java:76)
com.github.copilot.status.CopilotStatusService.notifyApplication(CopilotStatusService.java:64)
com.github.copilot.github.GitHubAuthStartupActivity.handleAuthNotifications(GitHubAuthStartupActivity.java:54)
com.github.copilot.github.GitHubAuthStartupActivity.execute(GitHubAuthStartupActivity.java:35)
com.intellij.ide.startup.impl.StartupManagerImpl$runPostStartupActivities$4$1$1.invokeSuspend(StartupManagerImpl.kt:254)
com.intellij.ide.startup.impl.StartupManagerImpl$runPostStartupActivities$4$1$1.invoke(StartupManagerImpl.kt)
com.intellij.ide.startup.impl.StartupManagerImpl$runPostStartupActivities$4$1$1.invoke(StartupManagerImpl.kt)
kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:89)
kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:169)
kotlinx.coroutines.BuildersKt.withContext(Unknown Source)
com.intellij.ide.startup.impl.StartupManagerImpl$runPostStartupActivities$4$1.invokeSuspend(StartupManagerImpl.kt:253)
kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:106)
kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:570)
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:750)
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:677)
kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:664)
