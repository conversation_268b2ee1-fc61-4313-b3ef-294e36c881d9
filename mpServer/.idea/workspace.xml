<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="529fff03-1fc2-4697-83c5-0e9f06b52a93" name="Changes" comment="fix">
      <change beforePath="$PROJECT_DIR$/../client/src/main/java/env/Global.java" beforeDir="false" afterPath="$PROJECT_DIR$/../client/src/main/java/env/Global.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../client/src/main/java/net/MyNetworkDelegate.java" beforeDir="false" afterPath="$PROJECT_DIR$/../client/src/main/java/net/MyNetworkDelegate.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../client/src/main/java/view/Base.java" beforeDir="false" afterPath="$PROJECT_DIR$/../client/src/main/java/view/Base.java" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Go File" />
      </list>
    </option>
  </component>
  <component name="GOROOT" url="file://$USER_HOME$/sdk/go1.19.4" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2eXDS9uXTUXjJ9mpRUOhPivd3aF" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;DefaultGoTemplateProperty&quot;: &quot;Go File&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.formatter.settings.were.checked&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.migrated.go.modules.settings&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.modules.automatic.dependencies.download&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.modules.go.list.on.any.changes.was.set&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;configurable.GoLibrariesConfigurable.is.expanded&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;go.import.settings.migrated&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;/Users/<USER>/Desktop/world-v5/server/ws&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.github.copilot.settings.ApplicationConfigurable&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/ws" />
      <recent name="$PROJECT_DIR$" />
      <recent name="$PROJECT_DIR$/db" />
      <recent name="$PROJECT_DIR$/ut" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="go build main.go" type="GoApplicationRunConfiguration" factoryName="Go Application" temporary="true" nameIsGenerated="true">
      <module name="server" />
      <working_directory value="$PROJECT_DIR$" />
      <kind value="FILE" />
      <package value="server" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$/main.go" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Go Build.go build main.go" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-gosdk-33c477a475b1-b97fc8a1e17c-org.jetbrains.plugins.go.sharedIndexes.bundled-GO-241.14494.238" />
        <option value="bundled-js-predefined-1d06a55b98c1-74d2a5396914-JavaScript-GO-241.14494.238" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="http server初始" />
    <MESSAGE value="接口通过测试" />
    <MESSAGE value="记录" />
    <MESSAGE value="1" />
    <MESSAGE value="不暴露xor的值，换成time" />
    <MESSAGE value="生成sign" />
    <MESSAGE value="注册码fix" />
    <MESSAGE value="验签失败时加个打印" />
    <MESSAGE value="服务器返回数据时可配置是否加密" />
    <MESSAGE value="fix token登录没有判断机器码是否被修改" />
    <MESSAGE value="fix  偶尔登录没有下发权限的问题" />
    <MESSAGE value="fix" />
    <option name="LAST_COMMIT_MESSAGE" value="fix" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
</project>