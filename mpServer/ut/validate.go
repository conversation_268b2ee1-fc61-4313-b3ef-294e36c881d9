package ut

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"log"
	"reflect"
	"strings"
)

const (
	TRIM = "trim"
	MD5  = "md5"
)

// Validate
/*
 * @description
 * @param paramName 参数的名称
 * @param validateTag 验证的tag
 * @param v 参数的值
 * @param cv
 * @param exists 上层ctxGet的时候，有没有get到（因为上层ctx没拿到也会设置成0值）
 * @return msg
 * @return outVal
 */
func Validate(paramName, validateTag string, v any, cv reflect.Value, exists bool, output func(v reflect.Value)) (msg string) {
	split := strings.Split(validateTag, ",")
	// 优先进行trim 和 md5生成
	lo.ForEach(split, func(vs string, index int) {
		if reflect.TypeOf(v).Kind() == reflect.String {
			if vs == TRIM {
				v = Trim(cast.ToString(v))
				if output != nil {
					output(reflect.ValueOf(v))
				}
			}
			if vs == MD5 {
				sum := md5.Sum([]byte(cast.ToString(v)))
				v = hex.EncodeToString(sum[:])
				if output != nil {
					output(reflect.ValueOf(v))
				}
			}
		}
	})
	// 验证其他
	for _, vs := range split {
		if vs == "required" && !exists {
			return fmt.Sprintf("参数列表不正确")
		}
		// 区间验证 validate:"in=1|2|3" (字段只能是1或者2或者3)
		if strings.HasPrefix(vs, "in=") {
			charge := vs[3:]
			after := strings.Split(charge, "|")
			l := len(after)
			if l > 0 {
				tv := cast.ToFloat64(v)
				for _, s := range after {
					if tv == cast.ToFloat64(s) {
						l = 0
						break
					}
				}
				if l > 0 {
					return fmt.Sprintf("参数:%s必须在列:%v", paramName, after)
				}
			}
		}
		if strings.HasPrefix(vs, "gt=") {
			charge := cast.ToFloat64(vs[3:])
			tv := cast.ToFloat64(v)
			if tv <= charge {
				return fmt.Sprintf("参数:%s必须大于:%f", paramName, tv)
			}
		}
		if strings.HasPrefix(vs, "gte=") {
			charge := cast.ToFloat64(vs[4:])
			tv := cast.ToFloat64(v)
			if tv < charge {
				return fmt.Sprintf("参数:%s必须大于等于:%f", paramName, tv)
			}
		}
		if strings.HasPrefix(vs, "lt=") {
			charge := cast.ToFloat64(vs[3:])
			tv := cast.ToFloat64(v)
			if charge >= tv {
				return fmt.Sprintf("参数:%s必须小于:%f", paramName, tv)
			}
		}
		if strings.HasPrefix(vs, "lte=") {
			charge := cast.ToFloat64(vs[4:])
			tv := cast.ToFloat64(v)
			if charge > tv {
				return fmt.Sprintf("参数:%s必须小于等于:%f", paramName, tv)
			}
		}
		if strings.HasPrefix(vs, "len=") {
			if reflect.TypeOf(v).Kind() != reflect.String {
				return fmt.Sprintf("参数:%s必须是字符串.", paramName)
			}
			charge := cast.ToInt(vs[4:])
			if len(cast.ToString(v)) < charge {
				return fmt.Sprintf("参数:%s长度需要至少等于%d", paramName, charge)
			}
		}
		if strings.HasPrefix(vs, "xlen=") {
			if reflect.TypeOf(v).Kind() != reflect.String {
				return fmt.Sprintf("参数:%s必须是字符串.", paramName)
			}
			charge := cast.ToInt(vs[5:])
			if len(cast.ToString(v)) != charge {
				return fmt.Sprintf("参数:%s长度必须等于%d", paramName, charge)
			}
		}
		if strings.HasPrefix(vs, "deep") {
			kind := reflect.ValueOf(v).Kind()
			// 必须是结构体切片或者结构体
			if kind == reflect.Slice {
				if !cv.IsValid() {
					log.Println("deep参数跳过。")
					continue
				}
				for i := 0; i < cv.Len(); i++ {
					ele := cv.Index(i)
					for j := 0; j < ele.Elem().Type().NumField(); j++ {
						f := ele.Elem().Type().Field(j)
						_param := f.Tag.Get("param")
						_validate := f.Tag.Get("validate")
						inf := ele.Elem().Field(j).Interface()
						inf_cv := Convert(f, ele.Elem().Field(j))
						Validate(_param, _validate, inf, inf_cv, true, nil)
					}
				}
				continue
			}
			return fmt.Sprintf("deep参数类型验证失败:%s", paramName)
		}
	}

	return ""
}

// Convert 将sourceValue 转换为 targetKind类型
func Convert(targetKind reflect.StructField, sourceValue reflect.Value) reflect.Value {
	if targetKind.Type.Kind() == reflect.Slice {
		// 获取目标切片的类型信息
		sliceType := reflect.TypeOf(reflect.Zero(targetKind.Type).Interface())
		// 创建一个空的切片，类型与目标切片的类型相同
		newSlice := reflect.MakeSlice(sliceType, 0, 0)
		// 切片类型
		// sliceOf := reflect.SliceOf(sliceType)
		// 将传入的参数尝试转换,因为传入的参数已经被转换为map[string]interface{} 所以需要多处理一步判断
		bytes, err := json.Marshal(sourceValue.Interface())
		if err != nil {
			log.Printf("尝试转换切片数据传递出错: before=> %v\n", err.Error())
			return reflect.Value{}
		}
		// 将newSlice类型原始值转换为指针后使用反序列化
		ptr := reflect.New(sliceType).Interface()
		err = json.Unmarshal(bytes, ptr)
		if err != nil {
			log.Printf("尝试转换切片数据传递出错: after=> %v\n", err.Error())
			return reflect.Value{}
		}
		// 将解析后的值放回newSlice
		newSlice = reflect.ValueOf(ptr).Elem()
		return newSlice
	}
	// 基本数据类型
	if targetKind.Type.Kind() == sourceValue.Kind() {
		return sourceValue
	}
	switch targetKind.Type.Kind() {
	case reflect.String:
		return reflect.ValueOf(cast.ToString(sourceValue.Interface()))
	case reflect.Int:
		return reflect.ValueOf(cast.ToInt(sourceValue.Interface()))
	}
	log.Printf("未处理的参数传递类型 >> %v << !\n", targetKind)

	return reflect.Value{}
}
