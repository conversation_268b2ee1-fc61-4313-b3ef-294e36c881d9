package ut

import (
	"fmt"
	"github.com/dgrijalva/jwt-go"
	"github.com/google/uuid"
	"github.com/spf13/cast"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
)

const LoginTokenDuringTime = time.Hour * 144

func Now() int {
	return cast.ToInt(time.Now().UnixNano() / 1e6)
}

func TodayZeroTime() int {
	now := time.Now()
	midnight := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	return cast.ToInt(midnight.UnixNano() / 1e6)
}

func GenerateUID() string {
	return uuid.New().String()
}

func FirstToLower(str string) string {
	firstChar := str[:1]
	return strings.ToLower(firstChar) + str[1:]
}

func IsEmpty(str string) bool {
	return len(strings.ReplaceAll(str, " ", "")) == 0
}

func Trim(str string) string {
	return strings.Join(strings.Fields(str), "")
}

func GetApplicationDir() string {
	dir, _ := os.Getwd()
	return dir
}

func ReadFileContent(path string) (v string, err error) {
	if !strings.HasPrefix(path, "/") {
		path = fmt.Sprintf("/%s", path)
	}
	file, err := os.ReadFile(fmt.Sprintf("%s%s", GetApplicationDir(), path))
	if err != nil {
		return
	}
	v = string(file)
	return
}

func EncodeAccount2Token(accountId, userName string) (token string) {
	return GetTokenByHS256(&jwt.MapClaims{
		"exp":      time.Now().Add(LoginTokenDuringTime).Unix(),
		"userName": userName,
		"@id":      accountId,
	})
}

func GetRedirectUrlToken(id, realName, per string) (token string) {
	return GetTokenByHS256(&jwt.MapClaims{
		"exp":        time.Now().Add(LoginTokenDuringTime).Unix(),
		"timestamp":  time.Now().Add(LoginTokenDuringTime).UnixNano() / 1e6,
		"_id":        id,
		"userName":   realName,
		"permission": per,
	})
}

func WorkDir() (dir string) {
	var err error
	dir, err = os.Getwd()
	if err != nil {
		file, _ := exec.LookPath(os.Args[0])
		ApplicationPath, _ := filepath.Abs(file)
		dir, _ = filepath.Split(ApplicationPath)
	}
	return
}
