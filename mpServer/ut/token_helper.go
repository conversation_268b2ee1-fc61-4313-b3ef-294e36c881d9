package ut

import (
	"github.com/dgrijalva/jwt-go"
	"log"
	"time"
)

const secret = "Two Miles Game FEreWFW4GI3"

// GetTokenByHS256 使用hs256算法生成token
func GetTokenByHS256(v *jwt.MapClaims) string {
	return getToken(v, jwt.SigningMethodHS512, []byte(secret))
}

// getToken 生成token
func getToken(v *jwt.MapClaims, method jwt.SigningMethod, secretStr []byte) string {
	token := jwt.NewWithClaims(method, v)
	token.Header["kid"] = ""
	signedString, err := token.SignedString(secretStr)
	if err != nil {
		log.Printf("GetToken 签名错误:%v\n", err)
	}
	return signedString
}

// TryDecodeToken 解析token,失败返回nil,成功则返回原始数据,ignoreExpire=true时忽略token过期（如果设置exp则可以使用token.VerifyExpiresAt(time.Now().Unix(), true)判断是否过期）
func TryDecodeToken(token string, ignoreExpire bool) jwt.MapClaims {
	if token == "" {
		return nil
	}
	parse, _ := jwt.Parse(token, func(token *jwt.Token) (interface{}, error) {
		return secret, nil
	})
	if parse == nil {
		return nil
	}
	claims, ok := parse.Claims.(jwt.MapClaims)
	if !ok {
		return nil
	}
	if !ignoreExpire && !claims.VerifyExpiresAt(time.Now().Unix(), true) {
		return nil
	}
	return claims
}
