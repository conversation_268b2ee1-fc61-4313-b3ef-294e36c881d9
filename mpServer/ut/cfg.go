package ut

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"io"
	"os"
	"strings"
)

type Config struct {
	Db struct {
		Url  string `json:"url"`
		Name string `json:"name"`
	} `json:"db"`
	DefaultUser struct {
		Username string `json:"username"`
		Password string `json:"password"`
	} `json:"default"`
	Resource string `json:"resource"`
}

var _config *Config

func GetApplicationConfig() (config *Config) {
	if _config != nil {
		return _config
	}
	bytes, _ := os.ReadFile("config.json")
	err := json.Unmarshal(bytes, &_config)
	if err != nil {
		panic(err)
	}
	return _config
}

//var DevResource = "/Users/<USER>/Desktop/world-v5/client/src/test/build/"
//var ProResource = "/data/javaelf.cn/worldh5/"

func GetMd5Map() map[string]string {
	resource := GetApplicationConfig().Resource
	bytes, err := os.ReadFile(resource + "/md5.txt")
	if err != nil {
		panic("md5.txt not found")
	}
	lines := string(bytes)
	split := strings.Split(lines, "\n")
	data := make(map[string]string)
	for _, fileInfo := range split {
		filePathAndMd5 := strings.Split(fileInfo, ":")
		if len(filePathAndMd5) != 2 {
			continue
		}
		path := filePathAndMd5[0]
		md5 := filePathAndMd5[1]
		data[path] = md5
	}
	return data
}

func CalculateFileMD5(filePath string) (string, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	hash := md5.New()
	if _, err := io.Copy(hash, file); err != nil {
		return "", err
	}

	hashInBytes := hash.Sum(nil)[:16]
	return hex.EncodeToString(hashInBytes), nil
}
