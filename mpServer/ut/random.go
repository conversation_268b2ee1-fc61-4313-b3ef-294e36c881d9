package ut

import "math/big"

const (
	RANDOM_SEED_MASTER = 25214903917
	ADDEND             = 11
)

var RANDOM_SEED_MASK = new(big.Int).Sub(new(big.Int).Lsh(big.NewInt(1), 48), big.NewInt(1))
var MOD = new(big.Int).Lsh(big.NewInt(1), 64)

type Random struct {
	seed *big.Int
}

func NewRandom() *Random {
	r := &Random{
		seed: big.NewInt(0),
	}
	r.SetSeed(1)
	return r
}

func (r *Random) SetSeed(t int64) {
	r.seed = new(big.Int).And(new(big.Int).Xor(big.NewInt(t), big.NewInt(RANDOM_SEED_MASTER)), RANDOM_SEED_MASK)
	r.seed.Mod(r.seed, MOD)
}

func (r *Random) Next(t int) int64 {
	r.seed = new(big.Int).And(new(big.Int).Add(new(big.Int).Mul(r.seed, big.NewInt(RANDOM_SEED_MASTER)), big.NewInt(ADDEND)), RANDOM_SEED_MASK)
	r.seed.Mod(r.seed, MOD)
	i := new(big.Int).Rsh(r.seed, uint(48-t)).Int64()
	return unsignedInt2int(i)
}

func (r *Random) NextInt() int64 {
	return r.Next(32)
}

func unsignedInt2int(e int64) int64 {
	t := int64(4294967295) & e
	if t > 2147483647 {
		return -((int64(4294967295) ^ t) + 1)
	}
	return t
}
