package structs

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"server/db"
	"server/ut"
)

type Sign struct {
	Value      string       `bson:"value"`
	CreateTime int          `bson:"createTime"`
	Used       bool         `bson:"used"`       // 是否使用
	ChangeCnt  int          `bson:"changeCnt"`  // 每日可切换设备次数
	Permission map[int]bool `bson:"permission"` // 渠道启动权限
}

func (s *Sign) Define() (name string, index map[string]bool) {
	return "sign", map[string]bool{"value": true}
}

func (s *Sign) Save() (*mongo.UpdateResult, error) {
	bsonAuto := db.FieldToBsonAuto(s)
	if _, ok := bsonAuto["value"]; ok {
		delete(bsonAuto, "value")
	}
	filter := &bson.M{
		"value": s.Value,
	}
	return db.Col(s).UpdateOne(context.Background(), filter, &bson.M{"$set": bsonAuto}, options.Update().SetUpsert(true))
}

func GetSignFromDb(val string) (sign *Sign) {
	sign = &Sign{}
	single := db.Col(sign).FindOne(context.TODO(), &bson.M{"value": val})

	err := single.Decode(&sign)
	if err != nil {
		return nil
	}
	return sign
}

func GetNewSign(changeCnt int) (sign *Sign) {
	return &Sign{
		Value:      ut.GenerateUID(),
		CreateTime: ut.Now(),
		ChangeCnt:  changeCnt,
	}
}
