package structs

import (
	"context"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"server/db"
)

type User struct {
	Username              string `bson:"username"`
	Password              string `bson:"password"`
	Machine               string `bson:"machine"`               // 上次登录的机器码
	Token                 string `bson:"token"`                 // 当前token 每次使用账、密登录后都会更新
	Sign                  string `bson:"sign"`                  // 使用的注册码
	TodayChangeMachineCnt int    `bson:"todayChangeMachineCnt"` // 今日更换机器次数
	LastChangeMachineTime int    `bson:"lastChangeMachineTime"` // 上次更换机器时间
}

func (u *User) Define() (name string, index map[string]bool) {
	return "user", map[string]bool{"username": true, "password": false, "sign": false}
}

// Save
/*
 * @description 保存、新增
 * @return *mongo.UpdateResult
 * @return error
 */
func (u *User) Save() (*mongo.UpdateResult, error) {
	bsonAuto := db.FieldToBsonAuto(u)
	if _, ok := bsonAuto["username"]; ok {
		delete(bsonAuto, "username")
	}
	filter := &bson.M{
		"username": u.Username,
	}
	return db.Col(u).UpdateOne(context.Background(), filter, &bson.M{"$set": bsonAuto}, options.Update().SetUpsert(true))
}

func GetUser(username string) (user *User) {
	filter := &bson.M{
		"username": username,
	}
	u := &User{}

	singleResult := db.Col(u).FindOne(context.Background(), filter)
	if singleResult.Decode(&u) != nil {
		return nil
	}
	return u
}
