package main

import (
	"crypto/md5"
	"encoding/hex"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"io"
	"log"
	"os"
	"os/signal"
	"server/db"
	"server/path"
	"server/structs"
	"server/ut"
	"server/ws"
	"syscall"
)

func main() {
	// mongo db
	config := ut.GetApplicationConfig()
	db.Init(config.Db.Url, config.Db.Name)
	// write user
	sum := md5.Sum([]byte(cast.ToString(config.DefaultUser.Password)))
	root := &structs.User{
		Username: config.DefaultUser.Username,
		Password: hex.EncodeToString(sum[:]),
	}
	_, err := root.Save()
	if err != nil {
		panic("初始化root用户失败！")
	}
	gin.DefaultWriter = io.Discard
	go func() {
		middleware := ws.GetMiddleware()
		engine := middleware.Engine
		engine.Use(cross)
		middleware.WarpPostFunction(path.Login, &ws.Login{}, true).
			WarpPostFunction(path.TokenLogin, &ws.TokenLogin{}, true).
			WarpPostFunction(path.Register, &ws.Register{}, true).
			WarpPostFunction(path.Ping, &ws.Ping{}, true).
			WarpPostFunction(path.Md5Info, &ws.Md5Info{}, false).
			WarpPostFunction(path.GenSign, &ws.GenSign{}, true)
		engine.Run(":9001")
	}()
	// 监听退出信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	log.Println("退出。。。")
}

func cross(ctx *gin.Context) {
	ctx.Header("Access-Control-Allow-Origin", "*")
	ctx.Header("Access-Control-Allow-Methods", "POST")
	ctx.Header("Access-Control-Allow-Headers", "Access-Control-Allow-Headers, Content-Type, Authorization, Access-Control-Allow-Methods, Access-Control-Allow-Origin")
	ctx.Header("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Cache-Control, Content-Language, Content-Type")
	ctx.Header("Access-Control-Allow-Credentials", "true")
	if ctx.Request.Method != "POST" {
		ctx.AbortWithStatus(204)
		return
	}
	ctx.Next()
}
