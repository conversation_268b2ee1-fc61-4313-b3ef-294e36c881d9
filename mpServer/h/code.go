package h

const (
	ERROR   = -1
	SUCCESS = 0
)

// ResponseConstruct http请求返回
func ResponseConstruct(code int, data interface{}, notify string) map[string]interface{} {
	return map[string]interface{}{
		"code":   code,
		"data":   data,
		"notify": notify,
	}
}

// ResponseSuccessNoDataNoDesc  http成功 但无数据 无提示返回
func ResponseSuccessNoDataNoDesc() map[string]interface{} {
	return ResponseConstruct(SUCCESS, nil, "")
}

// ResponseSuccessWithDataWithDesc http成功 有数据 有提示返回
func ResponseSuccessWithDataWithDesc(data interface{}, desc string) map[string]interface{} {
	return ResponseConstruct(SUCCESS, data, desc)
}

// ResponseSuccessWithDataNoDesc http成功 有数据 无提示返回
func ResponseSuccessWithDataNoDesc(data interface{}) map[string]interface{} {
	return ResponseConstruct(SUCCESS, data, "")
}

// ResponseErrorNoDataNoDesc http失败 无数据 无提示返回
func ResponseErrorNoDataNoDesc() map[string]interface{} {
	return ResponseConstruct(ERROR, "", "")
}

// ResponseErrorNoDataWithDesc http失败 无数据 有提示返回
func ResponseErrorNoDataWithDesc(desc string) map[string]interface{} {
	return ResponseConstruct(ERROR, "", desc)
}

// ResponseErrorWithDataNoDesc http失败 有数据 无提示返回
func ResponseErrorWithDataNoDesc(data interface{}) map[string]interface{} {
	return ResponseConstruct(ERROR, data, "")
}

// ResponseErrorWithDataWithDesc http失败 有数据 有提示返回
func ResponseErrorWithDataWithDesc(data interface{}, desc string) map[string]interface{} {
	return ResponseConstruct(ERROR, data, desc)
}

// ResponseSuccessNoDataWithDesc http成功 无数据 有提示返回
func ResponseSuccessNoDataWithDesc(desc string) map[string]interface{} {
	return ResponseConstruct(SUCCESS, nil, desc)
}
