package ws

import (
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/spf13/cast"
	"log"
	"reflect"
	"server/h"
	"server/ut"
	"strings"
)

/**
 * 对于http请求的加密方式
 *  客户端发起都是application/json的形式
 *  客户端将需要发送的数据进行json.stringify后，得到一个数据字符串A
 *  对A进行md5加密得到一个字符串B
 *  对A使用TextEncoder进行编码得到一个byte数组C
 *  从1-127中随机一个数作为异或值X,并且保证连续的两次请求X值一定不相同
 *  对C进行一次遍历，每个byte与X进行异或运算，得到一个byte数组D
 *  将D进行base64编码得到一个字符串E
 * 最终发送的数据形式是 {key: B, iv: X, data: E}
 */

const green = "\033[97;42m"
const reset = "\u001B[0m"
const (
	httpOk    = 200
	httpError = 500
)

var mid *Middleware

func GetMiddleware() *Middleware {
	if mid == nil {
		mux := gin.Default()
		mid = &Middleware{
			Engine: mux,
			router: map[string]Router{},
		}
	}
	return mid
}

type Middleware struct {
	Engine *gin.Engine
	router map[string]Router
}

func (m *Middleware) WarpPostFunction(path string, r Router, encryptResponse bool) *Middleware {
	m.WarpFunction(path, "POST", r, encryptResponse)
	return m
}

func (m *Middleware) WarpGetFunction(path string, r Router, encryptResponse bool) *Middleware {
	m.WarpFunction(path, "GET", r, encryptResponse)
	return m
}

// WarpFunction
/*
 * @description 注册路由 只能注册一个handler
 * @param path
 * @param method
 * @param handlers
 */
func (m *Middleware) WarpFunction(path string, method string, r Router, encryptResponse bool) *Middleware {
	fun := lo.If(method == "POST", m.Engine.POST).Else(m.Engine.GET)
	handlerFunc := m.router[path]
	if handlerFunc != nil {
		panic("重复的路径注册: " + path)
	}
	m.router[path] = r
	fun(path, m.wrap(path, method, encryptResponse))
	return m
}

func (m *Middleware) wrap(path string, reqMethod string, encryptResponse bool) gin.HandlerFunc {
	p := h.ResponseErrorNoDataWithDesc("未知错误")
	return func(ctx *gin.Context) {
		r := m.router[path]
		if r == nil {
			ctx.Status(httpError)
			ctx.Abort()
			return
		}

		rv := reflect.ValueOf(r)
		//rt := reflect.TypeOf(r)
		ret := rv.Elem().Type()
		// 创建一个router
		newInstance := ut.Clone(r)
		nrv := reflect.ValueOf(newInstance)

		method := nrv.MethodByName("Handle")
		if method.IsNil() {
			ctx.Status(httpError)
			ctx.Abort()
			return
		}

		if ret.NumField() > 0 && !DecodeContentApplicationJson(ctx) {
			ctx.AbortWithStatus(httpError)
			return
		}

		reqId_, e := ctx.Get("reqId")
		if !e {
			log.Println("没有 reqId  why？？？")
			reqId_ = 0
		}
		reqId := cast.ToInt64(reqId_)

		for i := 0; i < ret.NumField(); i++ {
			field := ret.Field(i)
			paramTag := field.Tag.Get("param")
			if paramTag == "" {
				continue
			}
			value, exists := ctx.Get(paramTag)
			if !exists {
				// 赋予类型零值
				value = reflect.Zero(field.Type).Interface()
			}
			tag := field.Tag.Get("validate")
			refValue := reflect.ValueOf(value)
			newField := nrv.Elem().FieldByName(field.Name)
			structField, _ := nrv.Elem().Type().FieldByName(field.Name)
			// 类型转换
			refValue = ut.Convert(structField, refValue)
			if tag != "" {
				msg := ut.Validate(paramTag, tag, value, refValue, exists, func(v reflect.Value) {
					// 部分输出值需要重新赋值
					refValue = v
				})
				if msg != "" {
					ctx.JSON(httpOk, EncodeResponse(reqId, h.ResponseErrorNoDataWithDesc(msg)))
					ctx.Abort()
					return
				}
			}
			if newField.IsValid() {
				newField.Set(refValue)
			}
		}
		args := make([]reflect.Value, 0)
		args = append(args, reflect.ValueOf(ctx))
		returnValues := method.Call(args)
		if returnValues == nil || len(returnValues) == 0 {
			returnValues = make([]reflect.Value, 0)
			returnValues = append(returnValues, reflect.ValueOf(p))
		}
		if len(returnValues) > 1 {
			returnValues = returnValues[:1]
		}
		data, ok := (returnValues[0].Interface()).(map[string]interface{})
		if !ok || data == nil {
			data = p
		}
		reqUri := ctx.Request.RequestURI
		clientIP := ctx.ClientIP()
		if i := strings.Index(reqUri, "?"); i != -1 {
			reqUri = reqUri[:i]
		}
		log.Printf("[O] [%13s] [%s] %s[%s]%s %s", reqMethod, clientIP, green, reqUri, reset, EncodeUrlValues2Json(data))
		if encryptResponse {
			ctx.JSON(httpOk, EncodeResponse(reqId, data))
		} else {
			ctx.JSON(httpOk, data)
		}
		ctx.Abort()
	}
}

// DecodeContentApplicationJson 解析 application/json
func DecodeContentApplicationJson(ctx *gin.Context) bool {
	data, err := ctx.GetRawData()
	if err != nil {
		return false
	}
	tm := make(map[string]interface{})
	err = json.Unmarshal(data, &tm)
	if err != nil {
		return false
	}
	// 解参数
	_data, ok1 := tm["data"]
	_time, ok2 := tm["reqId"]
	_key, ok3 := tm["key"]
	if !ok1 || !ok2 || !ok3 {
		log.Println("解参数失败！！！")
		return false
	}
	// 解data
	bytes, err := base64.StdEncoding.DecodeString(_data.(string))
	if err != nil {
		log.Println("解data失败！！！")
		return false
	}
	random := ut.NewRandom()
	reqId := cast.ToInt64(_time)
	ctx.Set("reqId", reqId)
	random.SetSeed(reqId)
	ints := fmt.Sprintf("%d", random.NextInt())
	// 取最后两位 作为iv
	ivs := ints[len(ints)-2:]
	if strings.HasPrefix(ivs, "0") {
		ivs = ivs[1:]
	}
	iv := cast.ToUint8(ivs)
	for i := range bytes {
		bytes[i] ^= iv
	}
	body := string(bytes)
	// 解key
	bytes, err = base64.StdEncoding.DecodeString(_key.(string))
	if err != nil {
		log.Println("解key失败！！！")
		ctx.AbortWithStatusJSON(httpError, EncodeResponse(reqId, h.ResponseErrorNoDataWithDesc("验签失败")))
		return false
	}
	key := string(bytes)

	sum := md5.Sum([]byte(cast.ToString(body)))
	if hex.EncodeToString(sum[:]) != key {
		log.Printf("验签失败！！！, key: %s, body: %s\n", key, body)
		ctx.AbortWithStatusJSON(httpError, EncodeResponse(reqId, h.ResponseErrorNoDataWithDesc("验签失败")))
		return false
	}
	tm = make(map[string]interface{})
	err = json.Unmarshal([]byte(body), &tm)
	if err != nil {
		log.Println("数据有误！！！")
		ctx.AbortWithStatusJSON(httpError, EncodeResponse(reqId, h.ResponseErrorNoDataWithDesc("验签失败")))
		return false
	}

	CommonSetRequestParams(tm, ctx)
	return true
}

func CommonSetRequestParams(tm map[string]interface{}, ctx *gin.Context) {
	ctx.Set("@LEN", len(tm))
	for k, v := range tm {
		ctx.Set(k, v)
	}
	reqMethod := ctx.Request.Method
	reqUri := ctx.Request.RequestURI
	clientIP := ctx.ClientIP()
	if i := strings.Index(reqUri, "?"); i != -1 {
		reqUri = reqUri[:i]
	}
	token := ctx.Request.Header.Get("Authorization")
	if !ut.IsEmpty(token) {
		ctx.Set("@token", token)
	}

	log.Printf("[I] [%13s] [%s] %s[%s]%s %s", reqMethod, clientIP, green, reqUri, reset, EncodeUrlValues2Json(tm))
}

func EncodeUrlValues2Json(p map[string]interface{}) string {
	if p == nil {
		return ""
	}
	marshal, err := json.Marshal(p)
	if err != nil {
		return ""
	}
	return string(marshal)
}

func EncodeResponse(reqId int64, data map[string]any) map[string]any {
	resp := make(map[string]any, 0)
	resp["reqId"] = reqId
	if data == nil {
		return resp
	}
	bytes, _ := json.Marshal(data)
	// 计算md5
	sum := md5.Sum(bytes)
	bodyMd5 := hex.EncodeToString(sum[:])
	random := ut.NewRandom()
	random.SetSeed(reqId)
	ints := fmt.Sprintf("%d", random.NextInt())
	// 取最后两位 作为iv
	ivs := ints[len(ints)-2:]
	if strings.HasPrefix(ivs, "0") {
		ivs = ivs[1:]
	}
	iv := cast.ToUint8(ivs)
	// 对body进行异或
	for i := range bytes {
		bytes[i] ^= iv
	}
	// 对bodyStr进行base64编码
	body := base64.StdEncoding.EncodeToString(bytes)
	// 对bodyMd5进行base64编码
	key := base64.StdEncoding.EncodeToString([]byte(bodyMd5))
	resp["data"] = body
	resp["key"] = key
	return resp
}
