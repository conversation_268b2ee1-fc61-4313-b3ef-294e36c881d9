package ws

import (
	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"server/h"
	"server/structs"
	"server/ut"
	"time"
)

const exp = time.Hour * 24 * 30
const everyDayChangeMachineCnt = 3

type Login struct {
	Username string `param:"username" validate:"required,trim"` // param是用来标识客户端请求时的参数名称
	Password string `param:"password" validate:"required,trim,md5"`
	Machine  string `param:"machine" validate:"required,trim"`
}

func (l *Login) Handle(ctx *gin.Context) map[string]interface{} {
	user := structs.GetUser(l.Username)
	if user == nil || user.Password != l.Password || ut.IsEmpty(l.Machine) {
		return h.ResponseErrorNoDataWithDesc("用户名或密码错误")
	}

	// 权限数据
	per := make(map[int]bool)
	sign := structs.GetSignFromDb(user.Sign)
	if sign == nil {
		return h.ResponseErrorNoDataWithDesc("该账号注册码已经失效！")
	}
	per = sign.Permission
	// 切换机器码次数
	diffCnt := sign.ChangeCnt
	// 判断要不要清理次数
	if user.LastChangeMachineTime < ut.TodayZeroTime() {
		user.TodayChangeMachineCnt = 0
	}
	if user.Machine != l.Machine {
		// 检查当天更换机器次数
		if user.TodayChangeMachineCnt >= sign.ChangeCnt {
			return h.ResponseErrorNoDataWithDesc("登录失败，该账号当天无法更换机器!")
		}
		// 更新机器码
		user.Machine = l.Machine
		user.LastChangeMachineTime = ut.Now()
		user.TodayChangeMachineCnt++
	}
	diffCnt = sign.ChangeCnt - user.TodayChangeMachineCnt
	// 生成token
	user.Token = ut.GetTokenByHS256(&jwt.MapClaims{
		"username": user.Username,
		"machine":  user.Machine,
		"exp":      time.Now().Add(exp).Unix(),
	})
	// 保存
	user.Save()
	return h.ResponseSuccessWithDataWithDesc(map[string]interface{}{
		"token":                 user.Token,
		"username":              user.Username,
		"machine":               user.Machine,
		"sign":                  user.Sign,
		"todayChangeMachineCnt": diffCnt,
		"permission":            per,
	}, "登陆成功")
}

type TokenLogin struct {
	Token   string `param:"@token" validate:"required,trim"`
	Machine string `param:"machine" validate:"required,trim"`
}

func (t *TokenLogin) Handle(ctx *gin.Context) map[string]interface{} {
	mapClaims := ut.TryDecodeToken(t.Token, false)
	if mapClaims == nil {
		return h.ResponseErrorNoDataWithDesc("会话过期，需要重新登陆!")
	}
	username := mapClaims["username"]
	machine := mapClaims["machine"]
	// token的机器码和本机机器码不一致
	if ut.IsEmpty(t.Machine) || t.Machine != cast.ToString(machine) {
		return h.ResponseErrorNoDataWithDesc("会话验证失败，需要重新登陆!")
	}
	user := structs.GetUser(cast.ToString(username))
	if user == nil {
		return h.ResponseErrorNoDataWithDesc("会话验证失败,用户不存在!")
	}

	if !ut.IsEmpty(user.Token) && user.Token != t.Token || user.Machine != t.Machine {
		return h.ResponseErrorNoDataWithDesc("会话已经过期，请重新登录!")
	}
	// 权限数据
	per := make(map[int]bool)
	sign := structs.GetSignFromDb(user.Sign)
	if sign == nil {
		return h.ResponseErrorNoDataWithDesc("该账号注册码已经失效！")
	}
	// 切换机器码次数
	diffCnt := sign.ChangeCnt
	// 判断要不要清理次数
	if user.LastChangeMachineTime < ut.TodayZeroTime() {
		user.TodayChangeMachineCnt = 0
	}
	// 检查当天更换机器次数
	if user.TodayChangeMachineCnt >= sign.ChangeCnt && user.Machine != t.Machine {
		return h.ResponseErrorNoDataWithDesc("登录失败，该账号当天无法更换机器!")
	}
	diffCnt = sign.ChangeCnt - user.TodayChangeMachineCnt
	per = sign.Permission

	return h.ResponseSuccessWithDataWithDesc(map[string]interface{}{
		"username":              username,
		"machine":               user.Machine,
		"sign":                  user.Sign,
		"todayChangeMachineCnt": diffCnt,
		"permission":            per,
	}, "登陆成功")
}

type Ping struct {
	Token    string `param:"@token" validate:"required,trim"`
	Machine  string `param:"machine" validate:"required,trim"`
	Username string `param:"username" validate:"required,trim"`
}

func (p *Ping) Handle(ctx *gin.Context) map[string]interface{} {
	mapClaims := ut.TryDecodeToken(p.Token, false)
	if mapClaims == nil {
		return h.ResponseErrorNoDataWithDesc("会话过期，需要重新登陆!")
	}
	username := mapClaims["username"]
	machine := mapClaims["machine"]
	if username != p.Username || ut.IsEmpty(p.Username) {
		return h.ResponseErrorNoDataWithDesc("伪造token数据!")
	}
	if ut.IsEmpty(p.Machine) || p.Machine != cast.ToString(machine) {
		return h.ResponseErrorNoDataWithDesc("账号在其他的方登录!")
	}
	user := structs.GetUser(cast.ToString(username))
	if user == nil {
		return h.ResponseErrorNoDataWithDesc("会话验证失败,用户不存在!")
	}
	if user.Machine != p.Machine {
		return h.ResponseErrorNoDataWithDesc("账号在其他的方登录!")
	}
	if !ut.IsEmpty(user.Token) && user.Token != p.Token {
		return h.ResponseErrorNoDataWithDesc("账号在其他的方登录!")
	}
	return h.ResponseSuccessWithDataWithDesc(map[string]interface{}{
		"success": true,
	}, "")
}

/**
 * @document
 * @token 是保留字段,可以使用,但不能自定义,仅在登陆后才拥有.
 * @id 是保留字段,可以使用,但不能自定义,随token解析出现.
 *
 * 参与自动转换的参数有两个tag: param(必选),validate(可选),并且参数必须在结构体中导出,结构体的数据类型都定为基本类型.
 * 参数自动转换列表有顺序限制,例如:
 * Login struct {
 *		UserName string `param:"username" validate:"required"`
 *		Password string `param:"password" validate:"required"`
 *	}
 * param参数需要和前端传递的参数名一致,也就是以后端的为准,不然无法自动填充传递数据
 * 对于参数的校验validate标签,规定以下规则:
 * required : 字段必须存在,前端必须传值,但不会对值进行校验. 例: validate:"required"
 * trim: 字段会被去空格后传递,优先触发trim后才会进行后面的验证.
 * md5 : 优先触发,该值会进行md5加密后传输
 * gt  : 字段必须是数字,且必须大于预设值. 例: validate:"gt=10" (必须大于10)
 * gte : 字段必须是数字,且必须大于等于预设值. 例: validate:"gte=10" (必须大于等于10)
 * lt  : 字段必须是数字,且必须小于于预设值. 例: validate:"lt=10" (必须小于10)
 * lte : 字段必须是数字,且必须小于等于预设值. 例: validate:"lte=10" (必须小于等于10)
 * len : 字段长度至少满足预设值.  例: validate:"len=10" (字段长度必须大于等于10)
 * xlen: 字段长度必须满足预设值.  例: validate:"xlen=10" (字段长度等于10)
 * in  : 字段必须是数字,并且必需在一个类似数组的包含里面. 例: validate:"in=1|2|3" (字段只能是1或者2或者3)
 * deep: 字段必须是结构体切片或者结构体,如果结构体内每一个值也设置了validate标签,则也会触发检查.
 */
