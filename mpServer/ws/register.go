package ws

import (
	"github.com/gin-gonic/gin"
	"log"
	"server/h"
	"server/structs"
	"server/ut"
)

type Register struct {
	Username string `param:"username" validate:"required,trim"`
	Password string `param:"password" validate:"required,trim,md5"`
	Sign     string `param:"sign" validate:"required,trim"`
}

func (r *Register) Handle(ctx *gin.Context) map[string]interface{} {
	user := structs.GetUser(r.Username)
	if user != nil {
		return h.ResponseErrorNoDataWithDesc("用户名已经存在！")
	}
	user = &structs.User{
		Username: r.Username,
		Password: r.Password,
		Sign:     r.Sign,
	}

	//todo 判断sign合法性
	sign := structs.GetSignFromDb(r.Sign)

	if sign == nil {
		return h.ResponseErrorNoDataWithDesc("注册码不存在")
	}

	if sign.Used {
		return h.ResponseErrorNoDataWithDesc("注册码已经被使用")
	}

	// 保存
	_, err := user.Save()
	if err != nil {
		log.Printf("用户注册失败：%s", err.Error())
		return h.ResponseErrorNoDataWithDesc("服务器出错！")
	}
	sign.Used = true
	_, err = sign.Save()
	if err != nil {
		log.Printf("注册码使用失败：%s", err.Error())
		return h.ResponseErrorNoDataWithDesc("服务器出错！")
	}
	return h.ResponseSuccessWithDataWithDesc(map[string]interface{}{}, "注册成功")
}

type SignPermission struct {
	Channel int  `param:"channel" validate:"required,trim"`
	Has     bool `param:"has" validate:"required,trim"`
}

type GenSign struct {
	GmKey      string            `param:"key" validate:"required,trim"`
	Cnt        int               `param:"cnt" validate:"required,trim"`
	ChangeCnt  int               `param:"changeCnt" validate:"required,trim"`
	Permission []*SignPermission `param:"permission" validate:"required"`
}

func (g *GenSign) Handle(ctx *gin.Context) map[string]interface{} {
	config := ut.GetApplicationConfig()
	if g.GmKey != config.DefaultUser.Password || g.Cnt <= 0 {
		return h.ResponseErrorNoDataWithDesc("")
	}
	keys := make([]string, 0)

	if g.ChangeCnt <= 0 {
		g.ChangeCnt = 3
	}
	p := make(map[int]bool)
	for i := 0; i < g.Cnt; i++ {
		sign := structs.GetNewSign(g.ChangeCnt)
		for _, permission := range g.Permission {
			p[permission.Channel] = permission.Has
		}
		sign.Permission = p
		sign.Save()
		keys = append(keys, sign.Value)
	}
	return h.ResponseSuccessWithDataWithDesc(keys, "注册成功")
}
